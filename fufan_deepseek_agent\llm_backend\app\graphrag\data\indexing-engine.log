18:42:29,943 graphrag.cli.index INFO Logging enabled at E:\my_graphrag\graphrag_2.1.0\graphrag\data\indexing-engine.log
18:42:30,797 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
18:42:38,753 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
18:42:38,760 graphrag.cli.index INFO Starting pipeline run. dry_run=False
18:42:38,761 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "deepseek-chat",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "https://api.deepseek.com",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": false,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 10,
            "max_retry_wait": 10.0,
            "concurrent_requests": 25,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "text-embedding-3-small",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "https://ai.devtool.tech/proxy/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 10,
            "max_retry_wait": 10.0,
            "concurrent_requests": 25,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "csv",
        "base_dir": "input",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": ".*\\.csv$",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null,
        "local_output_dir": null,
        "mineru_api_url": null,
        "mineru_output_dir": null,
        "table_description_api_key": null,
        "table_description_model": null,
        "base_url": null,
        "image_description_api_key": null,
        "image_description_model": null,
        "image_description_base_url": null
    },
    "embed_graph": {
        "enabled": true,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "csv",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompts/extract_graph.txt",
        "entity_types": [
            "organization",
            "person",
            "geo",
            "event"
        ],
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompt_turn_output/summarize_descriptions_turn_zh.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": "prompt_turn_output/community_report_graph_turn_zh.txt",
        "text_prompt": "prompts/community_report_text_zh.txt",
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": "prompts/extract_claims.txt",
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": "prompts/local_search_system_prompt.txt",
        "chat_model_id": "default_chat_model",
        "embedding_model_id": "default_embedding_model",
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": "prompts/global_search_map_system_prompt.txt",
        "reduce_prompt": "prompts/global_search_reduce_system_prompt.txt",
        "chat_model_id": "default_chat_model",
        "knowledge_prompt": "prompts/global_search_knowledge_system_prompt.txt",
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": "prompts/drift_search_system_prompt.txt",
        "reduce_prompt": "prompts/drift_search_reduce_prompt.txt",
        "chat_model_id": "default_chat_model",
        "embedding_model_id": "default_embedding_model",
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 4096
    },
    "basic_search": {
        "prompt": "prompts/basic_search_system_prompt.txt",
        "chat_model_id": "default_chat_model",
        "embedding_model_id": "default_embedding_model",
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data\\output\\lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": null
}
18:42:38,763 graphrag.storage.file_pipeline_storage INFO Creating file storage at E:\my_graphrag\graphrag_2.1.0\graphrag\data
18:42:38,765 graphrag.index.input.factory INFO loading input from root_dir=input
18:42:38,765 graphrag.index.input.factory INFO using file storage for input
18:42:38,769 graphrag.index.input.csv INFO Loading csv files from input
18:42:38,771 graphrag.storage.file_pipeline_storage INFO search E:\my_graphrag\graphrag_2.1.0\graphrag\data\input for files matching .*\.csv$
18:42:38,781 graphrag.index.input.util INFO Found 1 InputFileType.csv files, loading 1
18:42:38,781 graphrag.index.input.util INFO Total number of unfiltered InputFileType.csv rows: 8
