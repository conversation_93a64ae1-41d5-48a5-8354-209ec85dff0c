#!/usr/bin/env python3
import requests

def verify_models():
    try:
        response = requests.get('http://localhost:11434/api/tags')
        models = response.json()
        print('✅ 已安装的模型:')
        for model in models['models']:
            name = model['name']
            size_mb = model['size'] // 1024 // 1024
            print(f'  📦 {name} (大小: {size_mb} MB)')
        
        # 检查是否有 bge-m3
        model_names = [m['name'] for m in models['models']]
        if any('bge-m3' in name for name in model_names):
            print('\n🎉 BGE-M3 embedding 模型已成功安装！')
            return True
        else:
            print('\n❌ 未找到 BGE-M3 模型')
            return False
            
    except Exception as e:
        print(f'❌ 验证失败: {e}')
        return False

if __name__ == "__main__":
    verify_models()
