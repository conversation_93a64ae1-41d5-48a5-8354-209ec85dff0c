2025-04-03 14:54:05,544 - dev-graphrag-indexing - INFO - 从目录加载配置: E:\my_graphrag\graphrag_2.1.0\graphrag
2025-04-03 14:54:05,545 - dev-graphrag-indexing - INFO - 使用数据目录: E:\my_graphrag\graphrag_2.1.0\graphrag\data
2025-04-03 14:54:05,545 - dev-graphrag-indexing - INFO - 日志保存在: E:\my_graphrag\graphrag_2.1.0\graphrag\data\logs
2025-04-03 14:54:05,545 - dev-graphrag-indexing - INFO - 索引方法: Standard
2025-04-03 14:54:05,546 - dev-graphrag-indexing - INFO - 增量更新: 是
2025-04-03 14:54:05,546 - dev-graphrag-indexing - INFO - 内存分析: 否
2025-04-03 14:54:05,546 - dev-graphrag-indexing - INFO - 使用指定配置文件: E:\my_graphrag\graphrag_2.1.0\graphrag\data\settings_csv.yaml
2025-04-03 14:54:05,556 - dev-graphrag-indexing - INFO - 开始构建索引...
2025-04-03 14:54:05,867 - dev-graphrag-indexing - ERROR - 索引过程中发生错误: Could not find entities.parquet in storage!
Traceback (most recent call last):
  File "E:\my_graphrag\graphrag_2.1.0\graphrag\dev\graphrag_indexing.py", line 105, in run_indexing
    index_result: list[PipelineRunResult] = await api.build_index(
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "E:\my_graphrag\graphrag_2.1.0\graphrag\graphrag\api\index.py", line 73, in build_index
    async for output in run_pipeline(
  File "E:\my_graphrag\graphrag_2.1.0\graphrag\graphrag\index\run\run_pipeline.py", line 92, in run_pipeline
    await update_dataframe_outputs(
  File "E:\my_graphrag\graphrag_2.1.0\graphrag\graphrag\index\update\incremental_index.py", line 113, in update_dataframe_outputs
    ) = await _update_entities_and_relationships(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\my_graphrag\graphrag_2.1.0\graphrag\graphrag\index\update\incremental_index.py", line 248, in _update_entities_and_relationships
    delta_entities = await load_table_from_storage("entities", delta_storage)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\my_graphrag\graphrag_2.1.0\graphrag\graphrag\utils\storage.py", line 21, in load_table_from_storage
    raise ValueError(msg)
ValueError: Could not find entities.parquet in storage!
2025-04-03 14:58:25,182 - dev-graphrag-indexing - INFO - 从目录加载配置: E:\my_graphrag\graphrag_2.1.0\graphrag
2025-04-03 14:58:25,183 - dev-graphrag-indexing - INFO - 使用数据目录: E:\my_graphrag\graphrag_2.1.0\graphrag\data
2025-04-03 14:58:25,183 - dev-graphrag-indexing - INFO - 日志保存在: E:\my_graphrag\graphrag_2.1.0\graphrag\data\logs
2025-04-03 14:58:25,183 - dev-graphrag-indexing - INFO - 索引方法: Standard
2025-04-03 14:58:25,183 - dev-graphrag-indexing - INFO - 增量更新: 是
2025-04-03 14:58:25,184 - dev-graphrag-indexing - INFO - 内存分析: 否
2025-04-03 14:58:25,184 - dev-graphrag-indexing - INFO - 使用指定配置文件: E:\my_graphrag\graphrag_2.1.0\graphrag\data\settings_csv.yaml
2025-04-03 14:58:25,202 - dev-graphrag-indexing - INFO - 开始构建索引...
2025-04-03 15:20:25,421 - dev-graphrag-indexing - INFO - 索引构建完成，处理结果:
2025-04-03 15:20:25,429 - dev-graphrag-indexing - INFO - 工作流名称: create_base_text_units	状态: 成功
2025-04-03 15:20:25,438 - dev-graphrag-indexing - INFO - 工作流名称: create_final_documents	状态: 成功
2025-04-03 15:20:25,446 - dev-graphrag-indexing - INFO - 工作流名称: extract_graph	状态: 成功
2025-04-03 15:20:25,455 - dev-graphrag-indexing - INFO - 工作流名称: finalize_graph	状态: 成功
2025-04-03 15:20:25,464 - dev-graphrag-indexing - INFO - 工作流名称: create_communities	状态: 成功
2025-04-03 15:20:25,473 - dev-graphrag-indexing - INFO - 工作流名称: create_final_text_units	状态: 成功
2025-04-03 15:20:25,481 - dev-graphrag-indexing - INFO - 工作流名称: create_community_reports	状态: 成功
2025-04-03 15:20:25,489 - dev-graphrag-indexing - INFO - 工作流名称: generate_text_embeddings	状态: 成功
