14:36:52,164 graphrag.cli.index INFO Logging enabled at E:\my_graphrag\graphrag_2.1.0\graphrag\data\logs\indexing-engine.log
14:36:53,19 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:00,690 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:37:00,696 graphrag.cli.index INFO Starting pipeline run. dry_run=False
14:37:00,698 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "deepseek-chat",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "https://api.deepseek.com",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": false,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 10,
            "max_retry_wait": 10.0,
            "concurrent_requests": 25,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "text-embedding-3-small",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "https://ai.devtool.tech/proxy/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 10,
            "max_retry_wait": 10.0,
            "concurrent_requests": 25,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data\\logs",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data\\output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data\\update_output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "text",
        "base_dir": "input",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": ".*\\.txt$",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null,
        "local_output_dir": null,
        "mineru_api_url": null,
        "mineru_output_dir": null,
        "table_description_api_key": null,
        "table_description_model": null,
        "base_url": null,
        "image_description_api_key": null,
        "image_description_model": null,
        "image_description_base_url": null
    },
    "embed_graph": {
        "enabled": true,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "tokens",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompt_turn_output/extract_graph_zh.txt",
        "entity_types": [
            "company",
            "person",
            "product",
            "technology",
            "service",
            "location",
            "university",
            "investment",
            "acquisition",
            "operating system",
            "artificial intelligence",
            "quantum computing",
            "ecosystem",
            "competition",
            "collaboration",
            "market value",
            "innovation",
            "industry",
            "economic impact"
        ],
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompt_turn_output/summarize_descriptions_zh.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": "prompt_turn_output/community_report_graph_zh.txt",
        "text_prompt": "prompts/community_report_text_zh.txt",
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": "prompts/extract_claims.txt",
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": "prompts/local_search_system_prompt.txt",
        "chat_model_id": "default_chat_model",
        "embedding_model_id": "default_embedding_model",
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": "prompts/global_search_map_system_prompt.txt",
        "reduce_prompt": "prompts/global_search_reduce_system_prompt.txt",
        "chat_model_id": "default_chat_model",
        "knowledge_prompt": "prompts/global_search_knowledge_system_prompt.txt",
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": "prompts/drift_search_system_prompt.txt",
        "reduce_prompt": "prompts/drift_search_reduce_prompt.txt",
        "chat_model_id": "default_chat_model",
        "embedding_model_id": "default_embedding_model",
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 4096
    },
    "basic_search": {
        "prompt": "prompts/basic_search_system_prompt.txt",
        "chat_model_id": "default_chat_model",
        "embedding_model_id": "default_embedding_model",
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\data\\output\\lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": null
}
14:37:00,700 graphrag.storage.file_pipeline_storage INFO Creating file storage at E:\my_graphrag\graphrag_2.1.0\graphrag\data\output
14:37:00,701 graphrag.index.input.factory INFO loading input from root_dir=input
14:37:00,701 graphrag.index.input.factory INFO using file storage for input
14:37:00,703 graphrag.storage.file_pipeline_storage INFO search E:\my_graphrag\graphrag_2.1.0\graphrag\data\input for files matching .*\.txt$
14:37:00,706 graphrag.index.input.util INFO Found 1 InputFileType.text files, loading 1
14:37:00,706 graphrag.index.input.util INFO Total number of unfiltered InputFileType.text rows: 1
14:37:00,710 graphrag.index.run.run_pipeline INFO Final # of rows loaded: 1
14:37:00,721 graphrag.utils.storage INFO reading table from storage: documents.parquet
14:37:00,752 graphrag.utils.storage INFO reading table from storage: documents.parquet
14:37:00,756 graphrag.utils.storage INFO reading table from storage: text_units.parquet
14:37:00,786 graphrag.utils.storage INFO reading table from storage: text_units.parquet
14:37:01,508 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:01,513 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:01,516 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:01,518 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:01,524 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:22,458 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:37:54,675 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:38:29,50 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:39:10,428 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:40:01,720 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,247 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,248 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,255 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,256 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,256 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,259 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,261 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,262 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,264 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:41:46,312 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:43:16,738 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:43:16,773 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:43:16,773 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:43:16,774 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:43:16,776 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:43:16,783 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:18,60 graphrag.utils.storage INFO reading table from storage: entities.parquet
14:44:18,65 graphrag.utils.storage INFO reading table from storage: relationships.parquet
14:44:18,78 root INFO Starting preprocessing of transition probabilities on graph with 55 nodes and 67 edges
14:44:18,79 root INFO Starting at time 1743662658.0796456
14:44:18,79 root INFO Beginning preprocessing of transition probabilities for 55 vertices
14:44:18,79 root INFO Completed 1 / 55 vertices
14:44:18,79 root INFO Completed 6 / 55 vertices
14:44:18,79 root INFO Completed 11 / 55 vertices
14:44:18,79 root INFO Completed 16 / 55 vertices
14:44:18,79 root INFO Completed 21 / 55 vertices
14:44:18,79 root INFO Completed 26 / 55 vertices
14:44:18,79 root INFO Completed 31 / 55 vertices
14:44:18,79 root INFO Completed 36 / 55 vertices
14:44:18,79 root INFO Completed 41 / 55 vertices
14:44:18,79 root INFO Completed 46 / 55 vertices
14:44:18,79 root INFO Completed 51 / 55 vertices
14:44:18,79 root INFO Completed preprocessing of transition probabilities for vertices
14:44:18,79 root INFO Beginning preprocessing of transition probabilities for 67 edges
14:44:18,79 root INFO Completed 1 / 67 edges
14:44:18,79 root INFO Completed 7 / 67 edges
14:44:18,79 root INFO Completed 13 / 67 edges
14:44:18,80 root INFO Completed 19 / 67 edges
14:44:18,80 root INFO Completed 25 / 67 edges
14:44:18,80 root INFO Completed 31 / 67 edges
14:44:18,80 root INFO Completed 37 / 67 edges
14:44:18,80 root INFO Completed 43 / 67 edges
14:44:18,80 root INFO Completed 49 / 67 edges
14:44:18,80 root INFO Completed 55 / 67 edges
14:44:18,80 root INFO Completed 61 / 67 edges
14:44:18,80 root INFO Completed 67 / 67 edges
14:44:18,81 root INFO Completed preprocessing of transition probabilities for edges
14:44:18,81 root INFO Simulating walks on graph at time 1743662658.0816443
14:44:18,81 root INFO Walk iteration: 1/10
14:44:18,85 root INFO Walk iteration: 2/10
14:44:18,87 root INFO Walk iteration: 3/10
14:44:18,89 root INFO Walk iteration: 4/10
14:44:18,91 root INFO Walk iteration: 5/10
14:44:18,93 root INFO Walk iteration: 6/10
14:44:18,96 root INFO Walk iteration: 7/10
14:44:18,97 root INFO Walk iteration: 8/10
14:44:18,99 root INFO Walk iteration: 9/10
14:44:18,101 root INFO Walk iteration: 10/10
14:44:18,104 root INFO Learning embeddings at time 1743662658.1042352
14:44:18,104 gensim.models.word2vec INFO collecting all words and their counts
14:44:18,105 gensim.models.word2vec INFO PROGRESS: at sentence #0, processed 0 words, keeping 0 word types
14:44:18,105 gensim.models.word2vec INFO collected 55 word types from a corpus of 9600 raw words and 550 sentences
14:44:18,105 gensim.models.word2vec INFO Creating a fresh vocabulary
14:44:18,105 gensim.utils INFO Word2Vec lifecycle event {'msg': 'effective_min_count=0 retains 55 unique words (100.00% of original 55, drops 0)', 'datetime': '2025-04-03T14:44:18.105249', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'prepare_vocab'}
14:44:18,105 gensim.utils INFO Word2Vec lifecycle event {'msg': 'effective_min_count=0 leaves 9600 word corpus (100.00% of original 9600, drops 0)', 'datetime': '2025-04-03T14:44:18.105249', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'prepare_vocab'}
14:44:18,106 gensim.models.word2vec INFO deleting the raw counts dictionary of 55 items
14:44:18,106 gensim.models.word2vec INFO sample=0.001 downsamples 55 most-common words
14:44:18,106 gensim.utils INFO Word2Vec lifecycle event {'msg': 'downsampling leaves estimated 2547.3506758474205 word corpus (26.5%% of prior 9600)', 'datetime': '2025-04-03T14:44:18.106587', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'prepare_vocab'}
14:44:18,106 gensim.models.word2vec INFO estimated required memory for 55 words and 1536 dimensions: 703340 bytes
14:44:18,106 gensim.models.word2vec INFO resetting layer weights
14:44:18,108 gensim.utils INFO Word2Vec lifecycle event {'update': False, 'trim_rule': 'None', 'datetime': '2025-04-03T14:44:18.108500', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'build_vocab'}
14:44:18,108 gensim.utils INFO Word2Vec lifecycle event {'msg': 'training model with 8 workers on 55 vocabulary and 1536 features, using sg=1 hs=0 sample=0.001 negative=5 window=2 shrink_windows=True', 'datetime': '2025-04-03T14:44:18.108500', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'train'}
14:44:18,123 gensim.models.word2vec INFO EPOCH 0: training on 9600 raw words (2604 effective words) took 0.0s, 259179 effective words/s
14:44:18,136 gensim.models.word2vec INFO EPOCH 1: training on 9600 raw words (2540 effective words) took 0.0s, 268080 effective words/s
14:44:18,148 gensim.models.word2vec INFO EPOCH 2: training on 9600 raw words (2552 effective words) took 0.0s, 287003 effective words/s
14:44:18,148 gensim.utils INFO Word2Vec lifecycle event {'msg': 'training on 28800 raw words (7696 effective words) took 0.0s, 189708 effective words/s', 'datetime': '2025-04-03T14:44:18.148628', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'train'}
14:44:18,148 gensim.utils INFO Word2Vec lifecycle event {'params': 'Word2Vec<vocab=55, vector_size=1536, alpha=0.025>', 'datetime': '2025-04-03T14:44:18.148628', 'gensim': '4.3.3', 'python': '3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]', 'platform': 'Windows-11-10.0.22621-SP0', 'event': 'created'}
14:44:18,149 root INFO Completed. Ending time is 1743662658.149686 Elapsed time is -0.07004046440124512
14:44:18,210 graphrag.utils.storage INFO reading table from storage: entities.parquet
14:44:18,215 graphrag.utils.storage INFO reading table from storage: relationships.parquet
14:44:18,294 graphrag.utils.storage INFO reading table from storage: text_units.parquet
14:44:18,297 graphrag.utils.storage INFO reading table from storage: entities.parquet
14:44:18,301 graphrag.utils.storage INFO reading table from storage: relationships.parquet
14:44:18,351 graphrag.utils.storage INFO reading table from storage: relationships.parquet
14:44:18,354 graphrag.utils.storage INFO reading table from storage: entities.parquet
14:44:18,357 graphrag.utils.storage INFO reading table from storage: communities.parquet
14:44:18,369 graphrag.index.operations.summarize_communities.graph_context.context_builder INFO Number of nodes at level=1 => 49
14:44:18,430 graphrag.index.operations.summarize_communities.graph_context.context_builder INFO Number of nodes at level=0 => 55
14:44:19,271 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,271 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,272 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,273 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,273 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,277 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,281 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,283 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,284 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,284 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,288 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:44:19,290 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:48:37,653 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:48:37,683 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:48:37,694 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:48:37,694 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:48:37,712 httpx INFO HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
14:50:58,926 graphrag.utils.storage INFO reading table from storage: documents.parquet
14:50:58,930 graphrag.utils.storage INFO reading table from storage: relationships.parquet
14:50:58,934 graphrag.utils.storage INFO reading table from storage: text_units.parquet
14:50:58,938 graphrag.utils.storage INFO reading table from storage: entities.parquet
14:50:58,942 graphrag.utils.storage INFO reading table from storage: community_reports.parquet
14:50:58,947 graphrag.index.workflows.generate_text_embeddings INFO Creating embeddings
14:50:58,947 graphrag.index.operations.embed_text.embed_text INFO using vector store lancedb with container_name default for embedding entity.description: default-entity-description
14:50:59,601 graphrag.index.operations.embed_text.strategies.openai INFO embedding 59 inputs via 59 snippets using 4 batches. max_batch_size=16, max_tokens=8191
14:51:00,555 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:00,611 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:00,736 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:00,896 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:01,962 graphrag.index.operations.embed_text.embed_text INFO using vector store lancedb with container_name default for embedding community.full_content: default-community-full_content
14:51:01,970 graphrag.index.operations.embed_text.strategies.openai INFO embedding 17 inputs via 17 snippets using 2 batches. max_batch_size=16, max_tokens=8191
14:51:02,813 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:03,268 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:03,755 graphrag.index.operations.embed_text.embed_text INFO using vector store lancedb with container_name default for embedding text_unit.text: default-text_unit-text
14:51:03,760 graphrag.index.operations.embed_text.strategies.openai INFO embedding 5 inputs via 5 snippets using 1 batches. max_batch_size=16, max_tokens=8191
14:51:04,950 httpx INFO HTTP Request: POST https://ai.devtool.tech/proxy/v1/embeddings "HTTP/1.1 200 OK"
14:51:05,453 graphrag.cli.index INFO All workflows completed successfully.
