#!/usr/bin/env python3
"""
综合测试所有 API 配置
"""
import sys
sys.path.append('llm_backend')

from openai import OpenAI
from app.core.config import settings
import requests

def test_deepseek_api():
    """测试 DeepSeek API"""
    print("🧪 测试 DeepSeek API...")
    try:
        client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        
        response = client.chat.completions.create(
            model=settings.DEEPSEEK_MODEL,
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=20
        )
        
        print(f"✅ DeepSeek API 正常")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API 失败: {e}")
        return False

def test_openai_api():
    """测试 OpenAI API"""
    print("🧪 测试 OpenAI API...")
    try:
        client = OpenAI(
            api_key=settings.VISION_API_KEY,
            base_url=settings.VISION_BASE_URL
        )
        
        response = client.chat.completions.create(
            model=settings.VISION_MODEL,
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=20
        )
        
        print(f"✅ OpenAI API 正常")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API 失败: {e}")
        return False

def test_openai_embedding():
    """测试 OpenAI Embedding"""
    print("🧪 测试 OpenAI Embedding...")
    try:
        client = OpenAI(
            api_key=settings.VISION_API_KEY,
            base_url=settings.VISION_BASE_URL
        )
        
        response = client.embeddings.create(
            model="text-embedding-3-small",
            input="test"
        )
        
        print(f"✅ OpenAI Embedding 正常")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI Embedding 失败: {e}")
        return False

def test_ollama_api():
    """测试 Ollama API"""
    print("🧪 测试 Ollama API...")
    try:
        response = requests.get(f"{settings.OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            model_names = [m['name'] for m in models.get('models', [])]
            if any('bge-m3' in name for name in model_names):
                print(f"✅ Ollama API 正常，BGE-M3 模型可用")
                return True
            else:
                print(f"⚠️  Ollama API 正常，但 BGE-M3 模型未安装")
                return False
        else:
            print(f"❌ Ollama API 响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama API 失败: {e}")
        return False

def main():
    print("🚀 综合测试所有 API 配置...")
    print("=" * 60)
    
    print("📋 配置总览:")
    print(f"  🔹 DeepSeek API: {settings.DEEPSEEK_API_KEY[:15]}...")
    print(f"  🔹 OpenAI API: {settings.VISION_API_KEY[:15]}...")
    print(f"  🔹 Ollama URL: {settings.OLLAMA_BASE_URL}")
    print(f"  🔹 服务选择: Chat={settings.CHAT_SERVICE}, Agent={settings.AGENT_SERVICE}")
    print()
    
    results = {}
    
    # 测试各个 API
    results['deepseek'] = test_deepseek_api()
    results['openai'] = test_openai_api()
    results['openai_embedding'] = test_openai_embedding()
    results['ollama'] = test_ollama_api()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for service, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {service.upper()}: {'正常' if status else '异常'}")
    
    print(f"\n📊 总体状态: {success_count}/{total_count} 服务正常")
    
    if success_count >= 3:
        print("🎉 系统配置良好，可以正常运行！")
        print("\n💡 推荐配置:")
        print("  - 聊天功能: 使用 DeepSeek (成本低)")
        print("  - 视觉功能: 使用 OpenAI (质量高)")
        print("  - Embedding: 使用 Ollama BGE-M3 (本地化)")
        print("  - GraphRAG: 混合使用 (DeepSeek Chat + OpenAI Embedding)")
    else:
        print("⚠️  部分服务异常，请检查配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
