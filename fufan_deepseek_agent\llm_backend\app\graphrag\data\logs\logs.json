{"type": "error", "data": "Error running pipeline!", "stack": "Traceback (most recent call last):\n  File \"E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\graphrag\\index\\run\\run_pipeline.py\", line 149, in _run_pipeline\n    result = await workflow_function(config, context)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\graphrag\\index\\workflows\\extract_graph.py\", line 42, in run_workflow\n    summarization_strategy = config.summarize_descriptions.resolved_strategy(\n                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\my_graphrag\\graphrag_2.1.0\\graphrag\\graphrag\\config\\models\\summarize_descriptions_config.py\", line 46, in resolved_strategy\n    \"summarize_prompt\": (Path(root_dir) / self.prompt).read_text(\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python312\\Lib\\pathlib.py\", line 1027, in read_text\n    with self.open(mode='r', encoding=encoding, errors=errors) as f:\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python312\\Lib\\pathlib.py\", line 1013, in open\n    return io.open(self, mode, buffering, encoding, errors, newline)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: 'E:\\\\my_graphrag\\\\graphrag_2.1.0\\\\graphrag\\\\data\\\\prompt_turn_output\\\\summarize_descriptions_turn_zh.txt'\n", "source": "[Errno 2] No such file or directory: 'E:\\\\my_graphrag\\\\graphrag_2.1.0\\\\graphrag\\\\data\\\\prompt_turn_output\\\\summarize_descriptions_turn_zh.txt'", "details": null}