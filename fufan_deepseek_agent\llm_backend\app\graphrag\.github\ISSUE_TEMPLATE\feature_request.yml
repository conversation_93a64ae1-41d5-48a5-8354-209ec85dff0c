name: Feature Request
description: File a feature request
labels: ["enhancement"]
title: "[Feature Request]: <title>"

body:
  - type: checkboxes
    id: existingcheck
    attributes:
      label: Do you need to file an issue?
      description: Please help us manage our time by avoiding duplicates and common questions with the steps below.
      options:
        - label: I have searched the existing issues and this feature is not already filed.
        - label: My model is hosted on OpenAI or Azure. If not, please look at the "model providers" issue and don't file a new one here.
        - label: I believe this is a legitimate feature request, not just a question. If this is a question, please use the Discussions area.
  - type: textarea
    id: problem_description
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is.
      placeholder: What problem are you trying to solve?

  - type: textarea
    id: solution_description
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
      placeholder: How do you envision the solution?

  - type: textarea
    id: additional_context
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
      placeholder: Any additional information
