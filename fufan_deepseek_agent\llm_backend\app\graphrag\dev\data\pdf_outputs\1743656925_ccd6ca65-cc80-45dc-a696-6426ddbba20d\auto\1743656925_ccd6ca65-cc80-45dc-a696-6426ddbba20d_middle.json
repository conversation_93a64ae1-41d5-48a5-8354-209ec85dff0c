{"pdf_info": [{"preproc_blocks": [{"type": "title", "bbox": [113, 38, 484, 66], "lines": [{"bbox": [114, 43, 481, 63], "spans": [{"bbox": [114, 43, 481, 63], "score": 1.0, "content": "Deepseek企业级Agent项目开发实战", "type": "text"}]}], "index": 1, "virtual_lines": [{"bbox": [113, 38, 484, 47.333333333333336], "spans": [], "index": 0}, {"bbox": [113, 47.333333333333336, 484, 56.66666666666667], "spans": [], "index": 1}, {"bbox": [113, 56.66666666666667, 484, 66.0], "spans": [], "index": 2}]}, {"type": "title", "bbox": [98, 75, 496, 97], "lines": [{"bbox": [101, 78, 493, 97], "spans": [{"bbox": [101, 78, 493, 97], "score": 1.0, "content": "Part 4. Microsoft GraphRAG Query构建流程详解", "type": "text"}], "index": 3}], "index": 3}, {"type": "text", "bbox": [72, 107, 521, 187], "lines": [{"bbox": [94, 110, 510, 122], "spans": [{"bbox": [94, 110, 510, 122], "score": 1.0, "content": "在完成了 Microsoft GraphRAG 的索引构建及自定义接入图数据库 Neo4j 构建完整的知识图谱后，", "type": "text"}], "index": 4}, {"bbox": [74, 127, 520, 138], "spans": [{"bbox": [74, 127, 520, 138], "score": 1.0, "content": "我们在上一小节课程中已经初步实践了可以通过Cypher 语句来查询结构化数据中的信息。当然，传统的", "type": "text"}], "index": 5}, {"bbox": [75, 142, 485, 154], "spans": [{"bbox": [75, 142, 485, 154], "score": 1.0, "content": "Cypher 查询方式，需要我们具备一定的图数据库知识，对非技术人员来说，使用门槛较高。 而", "type": "text"}], "index": 6}, {"bbox": [75, 158, 515, 170], "spans": [{"bbox": [75, 158, 515, 170], "score": 1.0, "content": "Microsoft GraphRAG 则提供了一种更为直观、易用的查询方式，我们只需要输入自然语言查询，即可", "type": "text"}], "index": 7}, {"bbox": [74, 175, 176, 188], "spans": [{"bbox": [74, 175, 176, 188], "score": 1.0, "content": "获得结构化的查询结果。", "type": "text"}], "index": 8}], "index": 6}, {"type": "text", "bbox": [91, 196, 449, 210], "lines": [{"bbox": [93, 198, 449, 210], "spans": [{"bbox": [93, 198, 449, 210], "score": 1.0, "content": "这就需要我们了解 Microsoft GraphRAG 使用的第二阶段，即查询（Query）阶段。", "type": "text"}], "index": 9}], "index": 9}, {"type": "text", "bbox": [72, 219, 521, 332], "lines": [{"bbox": [93, 223, 496, 234], "spans": [{"bbox": [93, 223, 496, 234], "score": 1.0, "content": "索引阶段我们利用大语言模型结合提示工程，从非结构化文本（ .txt 、 .csv ）中提取出实体", "type": "text"}], "index": 10}, {"bbox": [79, 239, 505, 250], "spans": [{"bbox": [79, 239, 505, 250], "score": 1.0, "content": "（Entities）与关系（Relationships），构建出了基础的 Knowledge Graph ，并且通过建立层次化的", "type": "text"}], "index": 11}, {"bbox": [75, 255, 517, 268], "spans": [{"bbox": [75, 255, 517, 268], "score": 1.0, "content": "community  结构， community  以及 community_report  的丰富语义，相较于传统基于 Cypher 的查询", "type": "text"}], "index": 12}, {"bbox": [73, 271, 505, 283], "spans": [{"bbox": [73, 271, 505, 283], "score": 1.0, "content": "方式可以提供更多灵活性的 Query 操作， Microsoft GraphRAG  在项目开源之初是提供了 local  和", "type": "text"}], "index": 13}, {"bbox": [74, 287, 511, 300], "spans": [{"bbox": [74, 287, 511, 300], "score": 1.0, "content": "global  两种查询方式，分别对应了 local search  和 global search ，而后在不断的迭代更新过程", "type": "text"}], "index": 14}, {"bbox": [74, 304, 518, 316], "spans": [{"bbox": [74, 304, 518, 316], "score": 1.0, "content": "中，除了优化了 local search  和 global search  的效果，还新增了 DRIFT Search  和 Multi Index", "type": "text"}], "index": 15}, {"bbox": [74, 320, 369, 331], "spans": [{"bbox": [74, 320, 369, 331], "score": 1.0, "content": "Search  作为扩展优化的可选项，以进一步丰富 Query 操作的多样性。", "type": "text"}], "index": 16}], "index": 13}, {"type": "text", "bbox": [73, 342, 518, 371], "lines": [{"bbox": [94, 344, 518, 357], "spans": [{"bbox": [94, 344, 518, 357], "score": 1.0, "content": "Microsoft GraphRAG  在查询阶段构建的流程，相较于构建索引阶段会更为直观，核心的具体步骤", "type": "text"}], "index": 17}, {"bbox": [73, 358, 99, 373], "spans": [{"bbox": [73, 358, 99, 373], "score": 1.0, "content": "包括：", "type": "text"}], "index": 18}], "index": 17.5}, {"type": "text", "bbox": [85, 381, 188, 395], "lines": [{"bbox": [86, 384, 187, 395], "spans": [{"bbox": [86, 384, 187, 395], "score": 1.0, "content": "1. 接收用户的查询请求。", "type": "text"}], "index": 19}], "index": 19}, {"type": "text", "bbox": [84, 402, 345, 416], "lines": [{"bbox": [86, 405, 343, 416], "spans": [{"bbox": [86, 405, 343, 416], "score": 1.0, "content": "2. 根据查询所需的详细程度，选择合适的社区级别进行分析。", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [84, 423, 238, 437], "lines": [{"bbox": [85, 425, 236, 436], "spans": [{"bbox": [85, 425, 236, 436], "score": 1.0, "content": "3. 在选定的社区级别进行信息检索。", "type": "text"}], "index": 21}], "index": 21}, {"type": "text", "bbox": [84, 443, 227, 457], "lines": [{"bbox": [86, 444, 226, 456], "spans": [{"bbox": [86, 444, 226, 456], "score": 1.0, "content": "4. 依据社区摘要生成初步的响应。", "type": "text"}], "index": 22}], "index": 22}, {"type": "text", "bbox": [83, 464, 374, 478], "lines": [{"bbox": [86, 466, 372, 477], "spans": [{"bbox": [86, 466, 372, 477], "score": 1.0, "content": "5. 将多个相关社区的初步响应进行整合，形成一个全面的最终答案。", "type": "text"}], "index": 23}], "index": 23}, {"type": "text", "bbox": [72, 487, 516, 564], "lines": [{"bbox": [93, 490, 516, 501], "spans": [{"bbox": [93, 490, 516, 501], "score": 1.0, "content": "通过学习 Microsoft GraphRAG 索引构建的源码大家应该已经能够清晰的知道， Indexing  过程中", "type": "text"}], "index": 24}, {"bbox": [74, 506, 507, 518], "spans": [{"bbox": [74, 506, 507, 518], "score": 1.0, "content": "并不是在创建完第一层社区后就停止了，而是是分层的。也就是说，当创建第一层社区（即基础社区）", "type": "text"}], "index": 25}, {"bbox": [74, 521, 513, 533], "spans": [{"bbox": [74, 521, 513, 533], "score": 1.0, "content": "后，会将这些社区视为节点，进一步构建更高层级的社区。这种方法就实现在知识图谱中可以以不同的", "type": "text"}], "index": 26}, {"bbox": [74, 537, 513, 548], "spans": [{"bbox": [74, 537, 513, 548], "score": 1.0, "content": "粒度级别上组织和表示数据。比如第一层社区可以包含具体的实体或数据，而更高层级的社区则可以聚", "type": "text"}], "index": 27}, {"bbox": [75, 552, 232, 564], "spans": [{"bbox": [75, 552, 232, 564], "score": 1.0, "content": "合这些基础社区，形成更广泛的概览。", "type": "text"}], "index": 28}], "index": 26}, {"type": "text", "bbox": [72, 574, 514, 604], "lines": [{"bbox": [94, 576, 513, 588], "spans": [{"bbox": [94, 576, 513, 588], "score": 1.0, "content": "因此最核心的 Local Search  和 Global Search  的实现，就是源于不同的粒度级别而构建出来用", "type": "text"}], "index": 29}, {"bbox": [75, 592, 247, 604], "spans": [{"bbox": [75, 592, 247, 604], "score": 1.0, "content": "于处理不同类型问题的 Pipeline , 其中：", "type": "text"}], "index": 30}], "index": 29.5}, {"type": "text", "bbox": [83, 614, 264, 649], "lines": [{"bbox": [86, 617, 248, 627], "spans": [{"bbox": [86, 617, 248, 627], "score": 1.0, "content": "1. Local Search  是基于实体的检索。", "type": "text"}], "index": 31}, {"bbox": [85, 637, 263, 648], "spans": [{"bbox": [85, 637, 263, 648], "score": 1.0, "content": "2. Global Search  则是基于社区的检索。", "type": "text"}], "index": 32}], "index": 31.5}, {"type": "text", "bbox": [70, 682, 506, 713], "lines": [{"bbox": [93, 684, 503, 696], "spans": [{"bbox": [93, 684, 503, 696], "score": 1.0, "content": "因此接下来，我们就分别从源码层面，来详细介绍 Local Search  和 Global Search  的实现原", "type": "text"}], "index": 33}, {"bbox": [74, 701, 253, 713], "spans": [{"bbox": [74, 701, 253, 713], "score": 1.0, "content": "理，并实际操作不同检索方式的查询操作。", "type": "text"}], "index": 34}], "index": 33.5}, {"type": "text", "bbox": [93, 722, 276, 736], "lines": [{"bbox": [94, 724, 275, 735], "spans": [{"bbox": [94, 724, 275, 735], "score": 1.0, "content": "首先来介绍 Local Search ， 即本地检索。", "type": "text"}], "index": 35}], "index": 35}], "layout_bboxes": [], "page_idx": 0, "page_size": [594.9599609375, 841.9199829101562], "_layout_tree": [], "images": [], "tables": [], "interline_equations": [], "discarded_blocks": [], "need_drop": false, "drop_reason": [], "para_blocks": [{"type": "title", "bbox": [113, 38, 484, 66], "lines": [{"bbox": [114, 43, 481, 63], "spans": [{"bbox": [114, 43, 481, 63], "score": 1.0, "content": "Deepseek企业级Agent项目开发实战", "type": "text"}]}], "index": 1, "virtual_lines": [{"bbox": [113, 38, 484, 47.333333333333336], "spans": [], "index": 0}, {"bbox": [113, 47.333333333333336, 484, 56.66666666666667], "spans": [], "index": 1}, {"bbox": [113, 56.66666666666667, 484, 66.0], "spans": [], "index": 2}], "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562]}, {"type": "title", "bbox": [98, 75, 496, 97], "lines": [{"bbox": [101, 78, 493, 97], "spans": [{"bbox": [101, 78, 493, 97], "score": 1.0, "content": "Part 4. Microsoft GraphRAG Query构建流程详解", "type": "text"}], "index": 3}], "index": 3, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562]}, {"type": "text", "bbox": [72, 107, 521, 187], "lines": [{"bbox": [94, 110, 510, 122], "spans": [{"bbox": [94, 110, 510, 122], "score": 1.0, "content": "在完成了 Microsoft GraphRAG 的索引构建及自定义接入图数据库 Neo4j 构建完整的知识图谱后，", "type": "text"}], "index": 4}, {"bbox": [74, 127, 520, 138], "spans": [{"bbox": [74, 127, 520, 138], "score": 1.0, "content": "我们在上一小节课程中已经初步实践了可以通过Cypher 语句来查询结构化数据中的信息。当然，传统的", "type": "text"}], "index": 5}, {"bbox": [75, 142, 485, 154], "spans": [{"bbox": [75, 142, 485, 154], "score": 1.0, "content": "Cypher 查询方式，需要我们具备一定的图数据库知识，对非技术人员来说，使用门槛较高。 而", "type": "text"}], "index": 6}, {"bbox": [75, 158, 515, 170], "spans": [{"bbox": [75, 158, 515, 170], "score": 1.0, "content": "Microsoft GraphRAG 则提供了一种更为直观、易用的查询方式，我们只需要输入自然语言查询，即可", "type": "text"}], "index": 7}, {"bbox": [74, 175, 176, 188], "spans": [{"bbox": [74, 175, 176, 188], "score": 1.0, "content": "获得结构化的查询结果。", "type": "text"}], "index": 8}], "index": 6, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [74, 110, 520, 188]}, {"type": "text", "bbox": [91, 196, 449, 210], "lines": [{"bbox": [93, 198, 449, 210], "spans": [{"bbox": [93, 198, 449, 210], "score": 1.0, "content": "这就需要我们了解 Microsoft GraphRAG 使用的第二阶段，即查询（Query）阶段。", "type": "text"}], "index": 9}], "index": 9, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [93, 198, 449, 210]}, {"type": "text", "bbox": [72, 219, 521, 332], "lines": [{"bbox": [93, 223, 496, 234], "spans": [{"bbox": [93, 223, 496, 234], "score": 1.0, "content": "索引阶段我们利用大语言模型结合提示工程，从非结构化文本（ .txt 、 .csv ）中提取出实体", "type": "text"}], "index": 10}, {"bbox": [79, 239, 505, 250], "spans": [{"bbox": [79, 239, 505, 250], "score": 1.0, "content": "（Entities）与关系（Relationships），构建出了基础的 Knowledge Graph ，并且通过建立层次化的", "type": "text"}], "index": 11}, {"bbox": [75, 255, 517, 268], "spans": [{"bbox": [75, 255, 517, 268], "score": 1.0, "content": "community  结构， community  以及 community_report  的丰富语义，相较于传统基于 Cypher 的查询", "type": "text"}], "index": 12}, {"bbox": [73, 271, 505, 283], "spans": [{"bbox": [73, 271, 505, 283], "score": 1.0, "content": "方式可以提供更多灵活性的 Query 操作， Microsoft GraphRAG  在项目开源之初是提供了 local  和", "type": "text"}], "index": 13}, {"bbox": [74, 287, 511, 300], "spans": [{"bbox": [74, 287, 511, 300], "score": 1.0, "content": "global  两种查询方式，分别对应了 local search  和 global search ，而后在不断的迭代更新过程", "type": "text"}], "index": 14}, {"bbox": [74, 304, 518, 316], "spans": [{"bbox": [74, 304, 518, 316], "score": 1.0, "content": "中，除了优化了 local search  和 global search  的效果，还新增了 DRIFT Search  和 Multi Index", "type": "text"}], "index": 15}, {"bbox": [74, 320, 369, 331], "spans": [{"bbox": [74, 320, 369, 331], "score": 1.0, "content": "Search  作为扩展优化的可选项，以进一步丰富 Query 操作的多样性。", "type": "text"}], "index": 16}], "index": 13, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [73, 223, 518, 331]}, {"type": "text", "bbox": [73, 342, 518, 371], "lines": [{"bbox": [94, 344, 518, 357], "spans": [{"bbox": [94, 344, 518, 357], "score": 1.0, "content": "Microsoft GraphRAG  在查询阶段构建的流程，相较于构建索引阶段会更为直观，核心的具体步骤", "type": "text"}], "index": 17}, {"bbox": [73, 358, 99, 373], "spans": [{"bbox": [73, 358, 99, 373], "score": 1.0, "content": "包括：", "type": "text"}], "index": 18}], "index": 17.5, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [73, 344, 518, 373]}, {"type": "text", "bbox": [85, 381, 188, 395], "lines": [{"bbox": [86, 384, 187, 395], "spans": [{"bbox": [86, 384, 187, 395], "score": 1.0, "content": "1. 接收用户的查询请求。", "type": "text"}], "index": 19}], "index": 19, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [86, 384, 187, 395]}, {"type": "text", "bbox": [84, 402, 345, 416], "lines": [{"bbox": [86, 405, 343, 416], "spans": [{"bbox": [86, 405, 343, 416], "score": 1.0, "content": "2. 根据查询所需的详细程度，选择合适的社区级别进行分析。", "type": "text"}], "index": 20}], "index": 20, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [86, 405, 343, 416]}, {"type": "text", "bbox": [84, 423, 238, 437], "lines": [{"bbox": [85, 425, 236, 436], "spans": [{"bbox": [85, 425, 236, 436], "score": 1.0, "content": "3. 在选定的社区级别进行信息检索。", "type": "text"}], "index": 21}], "index": 21, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [85, 425, 236, 436]}, {"type": "text", "bbox": [84, 443, 227, 457], "lines": [{"bbox": [86, 444, 226, 456], "spans": [{"bbox": [86, 444, 226, 456], "score": 1.0, "content": "4. 依据社区摘要生成初步的响应。", "type": "text"}], "index": 22}], "index": 22, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [86, 444, 226, 456]}, {"type": "text", "bbox": [83, 464, 374, 478], "lines": [{"bbox": [86, 466, 372, 477], "spans": [{"bbox": [86, 466, 372, 477], "score": 1.0, "content": "5. 将多个相关社区的初步响应进行整合，形成一个全面的最终答案。", "type": "text"}], "index": 23}], "index": 23, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [86, 466, 372, 477]}, {"type": "text", "bbox": [72, 487, 516, 564], "lines": [{"bbox": [93, 490, 516, 501], "spans": [{"bbox": [93, 490, 516, 501], "score": 1.0, "content": "通过学习 Microsoft GraphRAG 索引构建的源码大家应该已经能够清晰的知道， Indexing  过程中", "type": "text"}], "index": 24}, {"bbox": [74, 506, 507, 518], "spans": [{"bbox": [74, 506, 507, 518], "score": 1.0, "content": "并不是在创建完第一层社区后就停止了，而是是分层的。也就是说，当创建第一层社区（即基础社区）", "type": "text"}], "index": 25}, {"bbox": [74, 521, 513, 533], "spans": [{"bbox": [74, 521, 513, 533], "score": 1.0, "content": "后，会将这些社区视为节点，进一步构建更高层级的社区。这种方法就实现在知识图谱中可以以不同的", "type": "text"}], "index": 26}, {"bbox": [74, 537, 513, 548], "spans": [{"bbox": [74, 537, 513, 548], "score": 1.0, "content": "粒度级别上组织和表示数据。比如第一层社区可以包含具体的实体或数据，而更高层级的社区则可以聚", "type": "text"}], "index": 27}, {"bbox": [75, 552, 232, 564], "spans": [{"bbox": [75, 552, 232, 564], "score": 1.0, "content": "合这些基础社区，形成更广泛的概览。", "type": "text"}], "index": 28}], "index": 26, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [74, 490, 516, 564]}, {"type": "text", "bbox": [72, 574, 514, 604], "lines": [{"bbox": [94, 576, 513, 588], "spans": [{"bbox": [94, 576, 513, 588], "score": 1.0, "content": "因此最核心的 Local Search  和 Global Search  的实现，就是源于不同的粒度级别而构建出来用", "type": "text"}], "index": 29}, {"bbox": [75, 592, 247, 604], "spans": [{"bbox": [75, 592, 247, 604], "score": 1.0, "content": "于处理不同类型问题的 Pipeline , 其中：", "type": "text"}], "index": 30}], "index": 29.5, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [75, 576, 513, 604]}, {"type": "index", "bbox": [83, 614, 264, 649], "lines": [{"bbox": [86, 617, 248, 627], "spans": [{"bbox": [86, 617, 248, 627], "score": 1.0, "content": "1. Local Search  是基于实体的检索。", "type": "text"}], "index": 31, "is_list_start_line": true}, {"bbox": [85, 637, 263, 648], "spans": [{"bbox": [85, 637, 263, 648], "score": 1.0, "content": "2. Global Search  则是基于社区的检索。", "type": "text"}], "index": 32, "is_list_start_line": true}], "index": 31.5, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [85, 617, 263, 648]}, {"type": "text", "bbox": [70, 682, 506, 713], "lines": [{"bbox": [93, 684, 503, 696], "spans": [{"bbox": [93, 684, 503, 696], "score": 1.0, "content": "因此接下来，我们就分别从源码层面，来详细介绍 Local Search  和 Global Search  的实现原", "type": "text"}], "index": 33}, {"bbox": [74, 701, 253, 713], "spans": [{"bbox": [74, 701, 253, 713], "score": 1.0, "content": "理，并实际操作不同检索方式的查询操作。", "type": "text"}], "index": 34}], "index": 33.5, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [74, 684, 503, 713]}, {"type": "text", "bbox": [93, 722, 276, 736], "lines": [{"bbox": [94, 724, 275, 735], "spans": [{"bbox": [94, 724, 275, 735], "score": 1.0, "content": "首先来介绍 Local Search ， 即本地检索。", "type": "text"}], "index": 35}], "index": 35, "page_num": "page_0", "page_size": [594.9599609375, 841.9199829101562], "bbox_fs": [94, 724, 275, 735]}]}], "_parse_type": "txt", "_version_name": "1.2.2"}