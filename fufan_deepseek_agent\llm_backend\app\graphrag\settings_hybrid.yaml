# GraphRAG 混合配置 - DeepSeek Chat + OpenAI Embedding
# 这个配置文件使用 DeepSeek 作为主要的聊天模型（成本更低），OpenAI 作为 Embedding 模型（质量更好）

encoding_model: cl100k_base

models:
  default_chat_model:
    type: openai_chat # DeepSeek 兼容 OpenAI API
    api_base: https://api.deepseek.com
    auth_type: api_key
    api_key: sk-de642f7292c3404cbd3a9dbd25217a7f
    model: deepseek-chat
    encoding_model: cl100k_base
    model_supports_json: false
    concurrent_requests: 25
    async_mode: threaded
    retry_strategy: native
    max_retries: -1
    tokens_per_minute: 0
    requests_per_minute: 0
    
  default_embedding_model:
    type: openai_embedding # 使用 OpenAI Embedding
    api_base: https://api.openai.com/v1
    auth_type: api_key
    api_key: ********************************************************************************************************************************************************************
    model: text-embedding-3-small
    encoding_model: cl100k_base
    model_supports_json: true
    concurrent_requests: 25
    async_mode: threaded
    retry_strategy: native
    max_retries: -1
    tokens_per_minute: 0
    requests_per_minute: 0

chunks:
  size: 300
  overlap: 100
  group_by_columns: [id]

input:
  type: file
  file_type: text
  base_dir: "input"
  file_encoding: utf-8
  file_pattern: ".*\\.txt$"

cache:
  type: file
  base_dir: "cache"

storage:
  type: file
  base_dir: "output"

reporting:
  type: file
  base_dir: "reporting"

entity_extraction:
  prompt: "prompts/entity_extraction.txt"
  entity_types: [organization,person,geo,event]
  max_gleanings: 0

summarize_descriptions:
  prompt: "prompts/summarize_descriptions.txt"
  max_length: 500

claim_extraction:
  prompt: "prompts/claim_extraction.txt"
  description: "Any claims or facts that could be relevant to information discovery."
  max_gleanings: 0

community_reports:
  prompt: "prompts/community_report.txt"
  max_length: 2000
  max_input_length: 8000

cluster_graph:
  max_cluster_size: 10

embed_graph:
  enabled: false

umap:
  enabled: false

snapshots:
  graphml: false
  raw_entities: false
  top_level_nodes: false

local_search:
  text_unit_prop: 0.5
  community_prop: 0.1
  conversation_history_max_turns: 5
  top_k_mapped_entities: 10
  top_k_relationships: 10
  max_tokens: 12000

global_search:
  max_tokens: 12000
  data_max_tokens: 12000
  map_max_tokens: 1000
  reduce_max_tokens: 2000
  concurrency: 32
