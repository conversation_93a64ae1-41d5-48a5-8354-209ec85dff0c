# Configuring GraphRAG Indexing

The GraphRAG system is highly configurable. This page provides an overview of the configuration options available for the GraphRAG indexing engine.

## Default Configuration Mode

The default configuration mode is the simplest way to get started with the GraphRAG system. It is designed to work out-of-the-box with minimal configuration. The primary configuration sections for the Indexing Engine pipelines are described below. The main ways to set up GraphRAG in Default Configuration mode are via:

- [Init command](init.md) (recommended)
- [Using YAML for deeper control](yaml.md)
- [Purely using environment variables](env_vars.md) (not recommended)
