name: Python Integration Tests
on:
  push:
    branches:
      - "**/main" # match branches like feature/main
      - "main"    # match the main branch
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - "**/main"
      - "main"
    paths-ignore:
      - "**/*.md"
      - ".semversioner/**"

permissions:
  contents: read
  pull-requests: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  # only run the for the latest commit
  cancel-in-progress: true

env:
  POETRY_VERSION: 1.8.3

jobs:
  python-ci:
    # skip draft PRs
    if: github.event.pull_request.draft == false
    strategy:
      matrix:
        python-version: ["3.10"]
        os: [ubuntu-latest, windows-latest]
      fail-fast: false # continue running all jobs even if one fails
    env:
      DEBUG: 1

    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            python:
              - 'graphrag/**/*'
              - 'poetry.lock'
              - 'pyproject.toml'
              - '**/*.py'
              - '**/*.toml'
              - '**/*.ipynb'
              - '.github/workflows/python*.yml'
              - 'tests/integration/**/*'

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Poetry
        uses: abatilo/actions-poetry@v3.0.0
        with:
          poetry-version: $POETRY_VERSION

      - name: Install dependencies
        shell: bash
        run: |
          poetry self add setuptools wheel
          poetry run python -m pip install gensim
          poetry install

      - name: Build
        run: |
          poetry build

      - name: Install Azurite
        id: azuright
        uses: potatoqualitee/azuright@v1.1

      # For more information on installation/setup of Azure Cosmos DB Emulator
      # https://learn.microsoft.com/en-us/azure/cosmos-db/how-to-develop-emulator?tabs=docker-linux%2Cpython&pivots=api-nosql
      # Note: the emulator is only available on Windows runners. It can take longer than the default to initially startup so we increase the default timeout.
      # If a job fails due to timeout, restarting the cicd job usually resolves the problem.
      - name: Install Azure Cosmos DB Emulator
        if: runner.os == 'Windows'
        run: |
          Write-Host "Launching Cosmos DB Emulator"
          Import-Module "$env:ProgramFiles\Azure Cosmos DB Emulator\PSModules\Microsoft.Azure.CosmosDB.Emulator"
          Start-CosmosDbEmulator -Timeout 500

      - name: Integration Test
        run: |
          poetry run poe test_integration
