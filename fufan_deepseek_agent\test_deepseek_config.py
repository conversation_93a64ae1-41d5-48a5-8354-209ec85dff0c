#!/usr/bin/env python3
"""
测试 DeepSeek API 配置
"""
import sys
sys.path.append('llm_backend')

from openai import OpenAI
from app.core.config import settings
import asyncio

def test_deepseek_chat():
    """测试 DeepSeek 聊天 API"""
    print("🧪 测试 DeepSeek 聊天 API...")
    try:
        client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        
        response = client.chat.completions.create(
            model=settings.DEEPSEEK_MODEL,
            messages=[
                {"role": "system", "content": "你是一个有用的AI助手。"},
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        print(f"✅ DeepSeek 聊天 API 测试成功!")
        print(f"📋 模型: {settings.DEEPSEEK_MODEL}")
        print(f"📋 响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek 聊天 API 测试失败: {e}")
        return False

def test_deepseek_reasoning():
    """测试 DeepSeek 推理能力"""
    print("\n🧪 测试 DeepSeek 推理能力...")
    try:
        client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        
        response = client.chat.completions.create(
            model="deepseek-reasoner",  # 如果支持推理模型
            messages=[
                {"role": "user", "content": "请解释一下什么是人工智能，并分析其发展趋势。"}
            ],
            max_tokens=200,
            temperature=0.3
        )
        
        print(f"✅ DeepSeek 推理模型测试成功!")
        print(f"📋 响应: {response.choices[0].message.content[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek 推理模型测试失败: {e}")
        print("ℹ️  可能不支持 deepseek-reasoner 模型，这是正常的")
        return False

def test_deepseek_function_calling():
    """测试 DeepSeek 函数调用能力"""
    print("\n🧪 测试 DeepSeek 函数调用能力...")
    try:
        client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        
        # 定义一个简单的函数
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "获取指定城市的天气信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city": {
                                "type": "string",
                                "description": "城市名称"
                            }
                        },
                        "required": ["city"]
                    }
                }
            }
        ]
        
        response = client.chat.completions.create(
            model=settings.DEEPSEEK_MODEL,
            messages=[
                {"role": "user", "content": "请帮我查询北京的天气"}
            ],
            tools=tools,
            tool_choice="auto",
            max_tokens=100
        )
        
        if response.choices[0].message.tool_calls:
            print(f"✅ DeepSeek 函数调用测试成功!")
            tool_call = response.choices[0].message.tool_calls[0]
            print(f"📋 调用函数: {tool_call.function.name}")
            print(f"📋 参数: {tool_call.function.arguments}")
        else:
            print(f"ℹ️  DeepSeek 没有调用函数，但响应正常")
            print(f"📋 响应: {response.choices[0].message.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek 函数调用测试失败: {e}")
        return False

def test_deepseek_streaming():
    """测试 DeepSeek 流式响应"""
    print("\n🧪 测试 DeepSeek 流式响应...")
    try:
        client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        
        stream = client.chat.completions.create(
            model=settings.DEEPSEEK_MODEL,
            messages=[
                {"role": "user", "content": "请用一句话介绍人工智能"}
            ],
            stream=True,
            max_tokens=50
        )
        
        print("✅ DeepSeek 流式响应测试:")
        print("📋 流式内容: ", end="")
        
        content = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content += chunk.choices[0].delta.content
                print(chunk.choices[0].delta.content, end="", flush=True)
        
        print(f"\n📊 总长度: {len(content)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek 流式响应测试失败: {e}")
        return False

def main():
    print("🚀 开始测试 DeepSeek API 配置...")
    print("=" * 60)
    
    # 显示配置信息
    print("📋 当前 DeepSeek 配置:")
    print(f"  - API Key: {settings.DEEPSEEK_API_KEY[:20]}...")
    print(f"  - Base URL: {settings.DEEPSEEK_BASE_URL}")
    print(f"  - Model: {settings.DEEPSEEK_MODEL}")
    print()
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试基础聊天
    if test_deepseek_chat():
        success_count += 1
    
    # 2. 测试推理能力
    if test_deepseek_reasoning():
        success_count += 1
    
    # 3. 测试函数调用
    if test_deepseek_function_calling():
        success_count += 1
    
    # 4. 测试流式响应
    if test_deepseek_streaming():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count >= 3:  # 至少3个测试通过就算成功
        print("🎉 DeepSeek API 配置测试基本通过！")
        print("✅ 项目可以正常使用 DeepSeek 相关功能")
    else:
        print("⚠️  多个测试失败，请检查 DeepSeek API 配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
