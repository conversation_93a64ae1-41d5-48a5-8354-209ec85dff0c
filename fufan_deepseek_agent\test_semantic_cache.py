#!/usr/bin/env python3
"""
测试项目的语义缓存功能
"""
import sys
import os
sys.path.append('llm_backend')

import asyncio
from app.services.redis_semantic_cache import RedisSemanticCache
from app.core.config import settings

async def test_semantic_cache():
    """测试语义缓存功能"""
    print("🧪 测试项目语义缓存功能...")
    print("=" * 50)
    
    try:
        # 创建缓存实例
        cache = RedisSemanticCache(prefix="test", user_id=1)
        
        # 测试消息
        test_messages = [
            {"role": "user", "content": "你好，请介绍一下你自己"}
        ]
        
        print("📝 测试消息:", test_messages[0]["content"])
        
        # 1. 测试 embedding 生成
        print("\n🔍 测试 Ollama embedding 生成...")
        embedding = await cache._get_embedding(test_messages[0]["content"])
        print(f"✅ Embedding 生成成功!")
        print(f"📊 向量维度: {len(embedding)}")
        print(f"📊 向量前5个值: {embedding[:5]}")
        
        # 2. 测试缓存存储
        print("\n💾 测试缓存存储...")
        test_response = "你好！我是一个AI助手，很高兴为您服务。"
        await cache.update(test_messages, test_response)
        print("✅ 缓存存储成功!")
        
        # 3. 测试缓存查找
        print("\n🔍 测试缓存查找...")
        cached_response = await cache.lookup(test_messages)
        if cached_response:
            print("✅ 缓存查找成功!")
            print(f"📄 缓存内容: {cached_response}")
        else:
            print("❌ 缓存查找失败")
            
        # 4. 测试相似问题的缓存命中
        print("\n🔍 测试相似问题缓存命中...")
        similar_messages = [
            {"role": "user", "content": "你好，能介绍下你自己吗"}  # 相似问题
        ]
        similar_cached = await cache.lookup(similar_messages)
        if similar_cached:
            print("✅ 相似问题缓存命中!")
            print(f"📄 缓存内容: {similar_cached}")
        else:
            print("❌ 相似问题未命中缓存")
            
        print("\n🎉 语义缓存功能测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 开始测试项目语义缓存功能...")
    print(f"📋 配置信息:")
    print(f"  - Ollama URL: {settings.OLLAMA_BASE_URL}")
    print(f"  - Embedding Model: {settings.OLLAMA_EMBEDDING_MODEL}")
    print(f"  - Cache Threshold: {settings.REDIS_CACHE_THRESHOLD}")
    print()
    
    # 运行异步测试
    asyncio.run(test_semantic_cache())

if __name__ == "__main__":
    main()
