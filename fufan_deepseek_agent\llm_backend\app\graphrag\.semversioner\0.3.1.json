{"changes": [{"description": "Add preflight check to check LLM connectivity.", "type": "patch"}, {"description": "Add streaming support for local/global search to query cli", "type": "patch"}, {"description": "Add support for both float and int on schema validation for community report generation", "type": "patch"}, {"description": "Avoid running index on gh-pages publishing", "type": "patch"}, {"description": "Implement Index API", "type": "patch"}, {"description": "Improves filtering for data dir inferring", "type": "patch"}, {"description": "Update to nltk 3.9.1", "type": "patch"}], "created_at": "2024-08-21T22:46:19+00:00", "version": "0.3.1"}